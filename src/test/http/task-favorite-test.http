### 任务收藏功能完整测试
### 测试场景：用户创建收藏夹，收藏任务，查询收藏状态
### 基于真实数据库数据进行测试

### ========================================
### 变量定义 - 基于真实数据库数据
### ========================================
@baseUrl = http://localhost:8080

# 用户信息（来自数据库）
@userId = 19
@userName = 马超
@userPhone = 17681012481
@tenantId = 1882955927490641921

# 任务ID（来自数据库 lalp_delivery_task 表，AUTO_INCREMENT=102）
@taskId1 = 1
@taskId2 = 2
@taskId3 = 3
@taskId4 = 4
@taskId5 = 5

# 收藏夹ID（测试过程中会创建）
@folderId1 = 1
@folderId2 = 2

# 后端管理Token（使用Liangma-Auth）
@backendToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2NvdW50SWQiOjE4NzkwNTQ3Mzg2NDQ5ODM4MDksInVzZXJfbmFtZSI6IjE4NTU1MzI5OTcxIiwic2NvcGUiOlsiYWxsIl0sImlwIjoiMTcyLjE2LjEuNTMiLCJuYW1lIjoi5byg6L-e576kIiwiYXZhdGFyIjpudWxsLCJleHAiOjE3NTM2NzE1NDYsInVzZXJOYW1lIjoiMTg1NTUzMjk5NzEiLCJ0eXBlIjoxLCJhdXRob3JpdGllcyI6WyIxODM0NDE4MzIxMzI4NDQzMzkzIiwiMTcxNTI1MTE4MjA3MzExODcyMSIsIjE4ODk1NDM2ODQzMzIxMTgwMTciLCIxNzE1MjUxMDg4NjQwODAyODE4IiwiMTgwMDc3MTY0MjIwNTIyNDk2MSIsIjE4MzQ0MTUyMzYzODAxMDI2NTciLCIxODM0NDE1NzI3NzAwODczMjE3IiwiMTgzNDQxNDA0MTQ3NzQxOTAwOSIsIjE3MTUyNTAyODQ2MzA4OTY2NDEiLCIxNjU5MzYyNTE4NTUyMzc5Mzk0IiwiMTcxNTI1MTI5MzE1MDg3MTU1MyIsIjE3MTUyNTEzMzY2MjkwMjY4MTgiLCIxODUwNzEzMjA4NDE2NjE2NDQ5IiwiMTcxNTI1MTI1MzI4ODIwNjMzNyIsIjE4MzQ0MTQ4Nzk5MTA0MDAwMDEiLCIxNzE1MjUwNTY0MTkzODQxMTUzIiwiMTcxNTI1MDc4NDU1Mzc2MjgxNyIsIjE4MzQ0MTYwMTg3ODk3NjUxMjIiXSwianRpIjoiMzJmMmJhNGItNGUzOS00NzdhLWE0NGQtZWFhY2UyOGMyNGUzIiwiY2xpZW50X2lkIjoidWF2ZmNfYWRtaW4ifQ.yTxHcHzFfQ3sKXATLSNrADkUZA2dwuwKrZBLGK3JMvc

### ========================================
### 步骤1: 查询现有收藏夹列表
### 了解当前系统中的收藏夹情况
### ========================================
GET {{baseUrl}}/api/backend/task-folders/list
Liangma-Auth: {{}}

### ========================================
### 步骤2: 创建第一个收藏夹 - 重要任务
### ========================================
POST {{baseUrl}}/api/backend/task-folders/add
Content-Type: application/json
Liangma-Auth: {{backendToken}}

{
  "folderName": "重要配送任务",
  "folderDesc": "需要重点关注的配送任务",
  "folderIcon": "star",
  "folderColor": "#f5222d",
  "isDefault": false,
  "sortOrder": 1
}

### ========================================
### 步骤3: 创建第二个收藏夹 - 紧急任务
### ========================================
POST {{baseUrl}}/api/backend/task-folders/add
Content-Type: application/json
Liangma-Auth: {{backendToken}}

{
  "folderName": "紧急处理任务",
  "folderDesc": "需要紧急处理的配送任务",
  "folderIcon": "fire",
  "folderColor": "#ff4d4f",
  "isDefault": false,
  "sortOrder": 2
}

### ========================================
### 步骤4: 创建默认收藏夹
### ========================================
POST {{baseUrl}}/api/backend/task-folders/add
Content-Type: application/json
Liangma-Auth: {{backendToken}}

{
  "folderName": "我的收藏",
  "folderDesc": "默认收藏夹",
  "folderIcon": "heart",
  "folderColor": "#faad14",
  "isDefault": true,
  "sortOrder": 0
}

### ========================================
### 步骤5: 再次查询收藏夹列表
### 验证收藏夹创建成功
### ========================================
GET {{baseUrl}}/api/backend/task-folders/list
Liangma-Auth: {{backendToken}}

### ========================================
### 步骤6: 快速收藏任务1（第一次收藏）
### 应该收藏成功，返回 favorited: true
### ========================================
POST {{baseUrl}}/api/backend/task-folders/quick-favorite/{{taskId1}}
Content-Type: application/json
Liangma-Auth: {{backendToken}}

### ========================================
### 步骤7: 快速收藏任务1（第二次收藏）
### 应该取消收藏，返回 favorited: false
### ========================================
POST {{baseUrl}}/api/backend/task-folders/quick-favorite/{{taskId1}}
Content-Type: application/json
Liangma-Auth: {{backendToken}}

### ========================================
### 步骤8: 快速收藏任务1（第三次收藏）
### 应该重新收藏，返回 favorited: true
### ========================================
POST {{baseUrl}}/api/backend/task-folders/quick-favorite/{{taskId1}}
Content-Type: application/json
Liangma-Auth: {{backendToken}}

### ========================================
### 步骤9: 快速收藏任务2
### ========================================
POST {{baseUrl}}/api/backend/task-folders/quick-favorite/{{taskId2}}
Content-Type: application/json
Liangma-Auth: {{backendToken}}

### ========================================
### 步骤10: 快速收藏任务3
### ========================================
POST {{baseUrl}}/api/backend/task-folders/quick-favorite/{{taskId3}}
Content-Type: application/json
Liangma-Auth: {{backendToken}}

### ========================================
### 步骤11: 将任务4收藏到指定收藏夹（重要任务）
### 注意：请将folderId替换为步骤2创建的收藏夹ID
### ========================================
POST {{baseUrl}}/api/backend/task-folders/add-task
Content-Type: application/json
Liangma-Auth: {{backendToken}}

{
  "folderId": {{folderId1}},
  "taskId": {{taskId4}},
  "favoriteNote": "这是一个重要的配送任务，需要重点关注",
  "favoriteTags": "重要,紧急"
}

### ========================================
### 步骤12: 将任务5收藏到指定收藏夹（紧急任务）
### 注意：请将folderId替换为步骤3创建的收藏夹ID
### ========================================
POST {{baseUrl}}/api/backend/task-folders/add-task
Content-Type: application/json
Liangma-Auth: {{backendToken}}

{
  "folderId": {{folderId2}},
  "taskId": {{taskId5}},
  "favoriteNote": "紧急处理的配送任务",
  "favoriteTags": "紧急,优先"
}

### ========================================
### 步骤13: 查询任务1的收藏状态
### 应该显示已收藏，包含收藏夹信息
### ========================================
GET {{baseUrl}}/api/backend/task-folders/task-favorite-status/{{taskId1}}
Liangma-Auth: {{backendToken}}

### ========================================
### 步骤14: 查询任务4的收藏状态
### 应该显示已收藏到指定收藏夹
### ========================================
GET {{baseUrl}}/api/backend/task-folders/task-favorite-status/{{taskId4}}
Liangma-Auth: {{backendToken}}

### ========================================
### 步骤15: 批量查询任务收藏状态
### 查询任务1-5的收藏状态
### ========================================
POST {{baseUrl}}/api/backend/task-folders/batch-favorite-status
Content-Type: application/json
Liangma-Auth: {{backendToken}}

[{{taskId1}}, {{taskId2}}, {{taskId3}}, {{taskId4}}, {{taskId5}}]

### ========================================
### 步骤16: 批量查询更多任务的收藏状态
### 包含一些未收藏的任务
### ========================================
POST {{baseUrl}}/api/backend/task-folders/batch-favorite-status
Content-Type: application/json
Liangma-Auth: {{backendToken}}

[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]

### ========================================
### 步骤17: 查询未收藏任务的状态
### 应该返回 favorited: false
### ========================================
GET {{baseUrl}}/api/backend/task-folders/task-favorite-status/99
Liangma-Auth: {{backendToken}}

### ========================================
### 步骤18: 尝试收藏不存在的任务
### 应该返回错误信息
### ========================================
POST {{baseUrl}}/api/backend/task-folders/quick-favorite/999
Content-Type: application/json
Liangma-Auth: {{backendToken}}

### ========================================
### 步骤19: 尝试收藏到不存在的收藏夹
### 应该返回错误信息
### ========================================
POST {{baseUrl}}/api/backend/task-folders/add-task
Content-Type: application/json
Liangma-Auth: {{backendToken}}

{
  "folderId": 999,
  "taskId": {{taskId1}},
  "favoriteNote": "测试错误情况",
  "favoriteTags": "测试"
}

### ========================================
### 步骤20: 使用无效token测试
### 应该返回认证失败
### ========================================
@invalidToken = invalid_token_here

GET {{baseUrl}}/api/backend/task-folders/list
Liangma-Auth: {{invalidToken}}

### ========================================
### 步骤21: 最终验证 - 查询所有收藏夹
### 验证数据完整性
### ========================================
GET {{baseUrl}}/api/backend/task-folders/list
Liangma-Auth: {{backendToken}}

### ========================================
### 步骤22: 最终验证 - 批量查询所有测试任务状态
### 验证收藏状态正确性
### ========================================
POST {{baseUrl}}/api/backend/task-folders/batch-favorite-status
Content-Type: application/json
Liangma-Auth: {{backendToken}}

[{{taskId1}}, {{taskId2}}, {{taskId3}}, {{taskId4}}, {{taskId5}}]

### ========================================
### 数据库验证SQL（在数据库中执行）
### ========================================

### 查看收藏夹数据
# SELECT * FROM lalp_task_folder WHERE deleted = 0 ORDER BY create_time DESC;

### 查看收藏关联数据
# SELECT
#     tf.id as favorite_id,
#     tf.task_id,
#     tf.folder_id,
#     tf.favorite_note,
#     tf.favorite_tags,
#     tf.creator_id,
#     tf.tenant_id,
#     folder.folder_name,
#     task.plan_name,
#     task.departure_point,
#     task.arrival_point,
#     task.status
# FROM lalp_task_favorite tf
# LEFT JOIN lalp_task_folder folder ON tf.folder_id = folder.id
# LEFT JOIN lalp_delivery_task task ON tf.task_id = task.id
# WHERE tf.deleted = 0
# ORDER BY tf.create_time DESC;

### 查看任务数据
# SELECT id, plan_name, departure_point, arrival_point, status, create_time
# FROM lalp_delivery_task
# WHERE id IN (1,2,3,4,5)
# ORDER BY id;

### 统计收藏数据
# SELECT
#     folder.folder_name,
#     COUNT(tf.id) as task_count
# FROM lalp_task_folder folder
# LEFT JOIN lalp_task_favorite tf ON folder.id = tf.folder_id AND tf.deleted = 0
# WHERE folder.deleted = 0
# GROUP BY folder.id, folder.folder_name
# ORDER BY folder.sort_order;
