package com.mascj.lalp.interfaces.rest.backend;

import com.mascj.lalp.application.service.TaskFolderService;
import com.mascj.lalp.application.service.UserService;
import com.mascj.lalp.domain.model.DeliveryTask;
import com.mascj.lalp.domain.model.TaskFavorite;
import com.mascj.lalp.domain.model.TaskFolder;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import com.mascj.lalp.infrastructure.common.api.PageResult;
import com.mascj.lalp.interfaces.rest.backend.dto.TaskFavoriteRequest;
import com.mascj.lalp.interfaces.rest.backend.dto.TaskFolderRequest;
import com.mascj.lalp.interfaces.rest.backend.dto.TaskFolderResponse;
import com.mascj.lalp.infrastructure.common.security.SecUtil;
import com.mascj.lalp.infrastructure.common.security.UserInfo;
import com.mascj.lalp.common.context.TenantContext;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 任务收藏夹控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/backend/task-folders")
@RequiredArgsConstructor

@Tag(name = "任务收藏夹管理", description = "任务收藏夹相关接口")
public class BackendTaskFolderController {

    private final TaskFolderService taskFolderService;
    private final UserService userService;

    /**
     * 从JWT获取当前用户信息
     * @param request HTTP请求
     * @return 当前用户信息
     */
    private CurrentUserInfo getCurrentUser(HttpServletRequest request) {
        try {
            // 使用现有的SecUtil从JWT获取用户信息
            UserInfo userInfo = SecUtil.getUserInfo(request);
            if (userInfo == null) {
                throw new IllegalArgumentException("用户未登录或token无效");
            }

            // 获取租户ID（从TenantContext或token中获取）
            Long tenantId = TenantContext.getTenantId();
            if (tenantId == null) {
                tenantId = 1L; // 默认租户ID
            }

            return new CurrentUserInfo(
                userInfo.getAccountId(),
                userInfo.getName() != null ? userInfo.getName() : userInfo.getUserName(),
                tenantId
            );
        } catch (Exception e) {
            log.error("获取当前用户信息失败", e);
            // 返回默认用户信息，但使用正确的租户ID
            Long tenantId = TenantContext.getTenantId();
            if (tenantId == null) {
                tenantId = 1L;
            }
            log.warn("使用默认用户信息进行测试，租户ID: {}", tenantId);
            return new CurrentUserInfo(1L, "系统用户", tenantId);
        }
    }

    /**
     * 当前用户信息内部类
     */
    private static class CurrentUserInfo {
        private final Long userId;
        private final String userName;
        private final Long tenantId;

        public CurrentUserInfo(Long userId, String userName, Long tenantId) {
            this.userId = userId;
            this.userName = userName;
            this.tenantId = tenantId;
        }

        public Long getUserId() { return userId; }
        public String getUserName() { return userName; }
        public Long getTenantId() { return tenantId; }
    }

    /**
     * 创建收藏夹
     *
     * @param request 创建请求
     * @return 创建结果
     */
    @Operation(summary = "创建收藏夹")
    @PostMapping("/add")
    public ApiResult<TaskFolderResponse> createFolder(@RequestHeader(SecUtil.LIANGMA_TOKEN) String token,
                                                     @RequestBody TaskFolderRequest request) {
        log.info("创建收藏夹: {}", request);

        TaskFolderResponse response = taskFolderService.createFolderWithAuth(token, request);
        return ApiResult.success(response);
    }

    /**
     * 更新收藏夹
     *
     * @param id 收藏夹ID
     * @param request 更新请求
     * @return 更新结果
     */
    @Operation(summary = "更新收藏夹")
    @PutMapping("/update/{id}")
    public ApiResult<TaskFolderResponse> updateFolder(
            @Parameter(description = "收藏夹ID", required = true) @PathVariable Long id,
            @RequestBody TaskFolderRequest request,
            HttpServletRequest httpRequest) {

        log.info("更新收藏夹: id={}, request={}", id, request);

        try {
            // 从JWT获取当前用户信息（更新时需要权限验证）
            CurrentUserInfo currentUser = getCurrentUser(httpRequest);

            TaskFolder folder = taskFolderService.updateFolder(id, request,
                    currentUser.getUserId(), currentUser.getTenantId());
            TaskFolderResponse response = TaskFolderResponse.of(folder);

            log.info("收藏夹更新成功: id={}, folderName={}", folder.getId(), folder.getFolderName());
            return ApiResult.success(response);

        } catch (IllegalArgumentException e) {
            log.warn("更新收藏夹失败: {}", e.getMessage());
            return ApiResult.badRequest(e.getMessage());
        } catch (Exception e) {
            log.error("更新收藏夹异常", e);
            return ApiResult.serverError("更新收藏夹失败: " + e.getMessage());
        }
    }

    /**
     * 删除收藏夹
     *
     * @param id 收藏夹ID
     * @return 删除结果
     */
    @Operation(summary = "删除收藏夹")
    @DeleteMapping("/delete/{id}")
    public ApiResult<Void> deleteFolder(
            @Parameter(description = "收藏夹ID", required = true) @PathVariable Long id,
            HttpServletRequest httpRequest) {

        log.info("删除收藏夹: id={}", id);

        try {
            // 从JWT获取当前用户信息（删除时需要权限验证）
            CurrentUserInfo currentUser = getCurrentUser(httpRequest);

            taskFolderService.deleteFolder(id, currentUser.getUserId(), currentUser.getTenantId());

            log.info("收藏夹删除成功: id={}", id);
            return ApiResult.success("删除成功", null);

        } catch (IllegalArgumentException e) {
            log.warn("删除收藏夹失败: {}", e.getMessage());
            return ApiResult.badRequest(e.getMessage());
        } catch (Exception e) {
            log.error("删除收藏夹异常", e);
            return ApiResult.serverError("删除收藏夹失败: " + e.getMessage());
        }
    }

    /**
     * 查询收藏夹列表
     *
     * @return 收藏夹列表
     */
    @Operation(summary = "查询收藏夹列表")
    @GetMapping("/list")
    public ApiResult<List<TaskFolderResponse>> listFolders(@RequestHeader(SecUtil.LIANGMA_TOKEN) String token) {
        log.info("查询收藏夹列表");

        List<TaskFolderResponse> responses = taskFolderService.listFoldersWithAuth(token);
        return ApiResult.success(responses);
    }

    /**
     * 查询可作为父级的收藏夹列表
     *
     * @param excludeId 排除的收藏夹ID（可选，用于编辑时避免循环引用）
     * @return 可选父级收藏夹列表
     */
    @Operation(summary = "查询可作为父级的收藏夹列表")
    @GetMapping("/list-available-parents")
    public ApiResult<List<TaskFolderResponse>> listAvailableParents(
            @Parameter(description = "排除的收藏夹ID", example = "1") @RequestParam(required = false) Long excludeId,
            HttpServletRequest httpRequest) {

        log.info("查询可选父级收藏夹: excludeId={}", excludeId);

        try {
            // 从JWT获取当前用户信息（获取租户ID）
            CurrentUserInfo currentUser = getCurrentUser(httpRequest);

            List<TaskFolder> folders = taskFolderService.listAvailableParents(currentUser.getTenantId(), excludeId);
            List<TaskFolderResponse> responses = folders.stream()
                    .map(TaskFolderResponse::of)
                    .collect(Collectors.toList());

            log.info("查询可选父级收藏夹成功: count={}", responses.size());
            return ApiResult.success(responses);

        } catch (Exception e) {
            log.error("查询可选父级收藏夹异常", e);
            return ApiResult.serverError("查询可选父级收藏夹失败: " + e.getMessage());
        }
    }

    /**
     * 查询指定父级下的子收藏夹列表
     *
     * @param parentId 父级收藏夹ID
     * @return 子收藏夹列表
     */
    @Operation(summary = "查询指定父级下的子收藏夹列表")
    @GetMapping("/list-children/{parentId}")
    public ApiResult<List<TaskFolderResponse>> listChildFolders(
            @Parameter(description = "父级收藏夹ID", required = true) @PathVariable Long parentId,
            HttpServletRequest httpRequest) {

        log.info("查询子收藏夹列表: parentId={}", parentId);

        try {
            // 从JWT获取当前用户信息（获取租户ID）
            CurrentUserInfo currentUser = getCurrentUser(httpRequest);

            List<TaskFolder> folders = taskFolderService.listChildFolders(parentId, currentUser.getTenantId());
            List<TaskFolderResponse> responses = folders.stream()
                    .map(TaskFolderResponse::of)
                    .collect(Collectors.toList());

            log.info("查询子收藏夹列表成功: parentId={}, count={}", parentId, responses.size());
            return ApiResult.success(responses);

        } catch (Exception e) {
            log.error("查询子收藏夹列表异常", e);
            return ApiResult.serverError("查询子收藏夹列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取收藏夹详情
     *
     * @param id 收藏夹ID
     * @return 收藏夹详情
     */
    @Operation(summary = "获取收藏夹详情")
    @GetMapping("/detail/{id}")
    public ApiResult<TaskFolderResponse> getFolderDetail(
            @Parameter(description = "收藏夹ID", required = true) @PathVariable Long id,
            HttpServletRequest httpRequest) {

        log.info("获取收藏夹详情: id={}", id);

        try {
            // 从JWT获取当前用户信息（获取租户ID）
            CurrentUserInfo currentUser = getCurrentUser(httpRequest);

            // 获取收藏夹详情（不需要用户验证，所有人都能查看）
            TaskFolder folder = taskFolderService.getFolderDetail(id, currentUser.getUserId(), currentUser.getTenantId());
            if (folder == null) {
                log.warn("收藏夹不存在: id={}", id);
                return ApiResult.notFound("收藏夹不存在");
            }

            TaskFolderResponse response = TaskFolderResponse.of(folder);
            log.info("获取收藏夹详情成功: id={}, folderName={}", folder.getId(), folder.getFolderName());
            return ApiResult.success(response);

        } catch (Exception e) {
            log.error("获取收藏夹详情异常", e);
            return ApiResult.serverError("获取收藏夹详情失败: " + e.getMessage());
        }
    }

    /**
     * 收藏任务到收藏夹
     *
     * @param request 收藏请求
     * @return 收藏结果
     */
    @Operation(summary = "收藏任务到收藏夹")
    @PostMapping("/add-task")
    public ApiResult<Void> addTaskToFolder(@RequestHeader(SecUtil.LIANGMA_TOKEN) String token,
                                          @RequestBody TaskFavoriteRequest request) {
        log.info("收藏任务: {}", request);

        taskFolderService.addTaskToFolderWithAuth(token, request);
        return ApiResult.success("收藏成功", null);
    }

    /**
     * 从收藏夹移除任务
     *
     * @param folderId 收藏夹ID
     * @param taskId 任务ID
     * @return 移除结果
     */
    @Operation(summary = "从收藏夹移除任务")
    @DeleteMapping("/remove-task/{folderId}/{taskId}")
    public ApiResult<Void> removeTaskFromFolder(
            @Parameter(description = "收藏夹ID", required = true) @PathVariable Long folderId,
            @Parameter(description = "任务ID", required = true) @PathVariable Long taskId,
            HttpServletRequest httpRequest) {

        log.info("移除任务收藏: folderId={}, taskId={}", folderId, taskId);

        try {
            // 从JWT获取当前用户信息（移除任务时需要权限验证）
            CurrentUserInfo currentUser = getCurrentUser(httpRequest);

            taskFolderService.removeTaskFromFolder(folderId, taskId, currentUser.getUserId(), currentUser.getTenantId());

            log.info("任务收藏移除成功: folderId={}, taskId={}", folderId, taskId);
            return ApiResult.success("移除成功", null);

        } catch (Exception e) {
            log.error("移除任务收藏异常", e);
            return ApiResult.serverError("移除任务收藏失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询收藏夹中的任务
     *
     * @param folderId 收藏夹ID
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 任务分页结果
     */
    @Operation(summary = "分页查询收藏夹中的任务")
    @GetMapping("/list-tasks/{folderId}")
    public ApiResult<PageResult<DeliveryTask>> listFolderTasks(
            @Parameter(description = "收藏夹ID", required = true) @PathVariable Long folderId,
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "页大小", example = "10") @RequestParam(defaultValue = "10") int pageSize,
            HttpServletRequest httpRequest) {

        log.info("查询收藏夹任务: folderId={}, pageNum={}, pageSize={}", folderId, pageNum, pageSize);

        try {
            // 从JWT获取当前用户信息（获取租户ID）
            CurrentUserInfo currentUser = getCurrentUser(httpRequest);

            // 查询收藏夹任务（不需要用户验证，所有人都能查看）
            PageResult<DeliveryTask> pageResult = taskFolderService.listFolderTasks(
                    folderId, pageNum, pageSize, currentUser.getUserId(), currentUser.getTenantId());

            log.info("查询收藏夹任务成功: folderId={}, total={}", folderId, pageResult.getTotal());
            return ApiResult.success(pageResult);

        } catch (IllegalArgumentException e) {
            log.warn("查询收藏夹任务失败: {}", e.getMessage());
            return ApiResult.badRequest(e.getMessage());
        } catch (Exception e) {
            log.error("查询收藏夹任务异常", e);
            return ApiResult.serverError("查询收藏夹任务失败: " + e.getMessage());
        }
    }

    /**
     * 快速收藏任务（收藏到默认收藏夹）
     *
     * @param taskId 任务ID
     * @return 收藏结果
     */
    @Operation(summary = "快速收藏任务", description = "将任务收藏到默认收藏夹，如果没有默认收藏夹则创建一个")
    @PostMapping("/quick-favorite/{taskId}")
    public ApiResult<Map<String, Object>> quickFavoriteTask(
            @RequestHeader(SecUtil.LIANGMA_TOKEN) String token,
            @Parameter(description = "任务ID", required = true) @PathVariable Long taskId) {

        log.info("快速收藏任务: taskId={}", taskId);

        Map<String, Object> result = taskFolderService.quickFavoriteTaskWithAuth(token, taskId);
        return ApiResult.success(result);
    }

    /**
     * 查询任务的收藏状态
     *
     * @param taskId 任务ID
     * @return 收藏状态
     */
    @Operation(summary = "查询任务收藏状态", description = "查询指定任务的收藏状态和收藏夹信息")
    @GetMapping("/task-favorite-status/{taskId}")
    public ApiResult<Map<String, Object>> getTaskFavoriteStatus(
            @RequestHeader(SecUtil.LIANGMA_TOKEN) String token,
            @Parameter(description = "任务ID", required = true) @PathVariable Long taskId) {

        log.info("查询任务收藏状态: taskId={}", taskId);

        Map<String, Object> result = taskFolderService.getTaskFavoriteStatusWithAuth(token, taskId);
        return ApiResult.success(result);
    }

    /**
     * 批量查询任务收藏状态
     *
     * @param taskIds 任务ID列表
     * @return 收藏状态映射
     */
    @Operation(summary = "批量查询任务收藏状态", description = "批量查询多个任务的收藏状态")
    @PostMapping("/batch-favorite-status")
    public ApiResult<Map<Long, Boolean>> getBatchTaskFavoriteStatus(
            @RequestHeader(SecUtil.LIANGMA_TOKEN) String token,
            @RequestBody List<Long> taskIds) {

        log.info("批量查询任务收藏状态: taskIds={}", taskIds);

        Map<Long, Boolean> result = taskFolderService.getBatchTaskFavoriteStatusWithAuth(token, taskIds);
        return ApiResult.success(result);
    }
}
