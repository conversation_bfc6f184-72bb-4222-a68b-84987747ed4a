### 任务收藏功能快速测试
### 基于您的数据库结构进行快速验证

### ========================================
### 变量定义
### ========================================
@baseUrl = http://localhost:8080

# 请替换为您的实际token
@backendToken = YOUR_BACKEND_TOKEN_HERE

# 基于您的数据库，任务ID应该在1-101之间
@taskId = 1

### ========================================
### 快速测试流程
### ========================================

### 1. 查询现有收藏夹
GET {{baseUrl}}/api/backend/task-folders/list
Liangma-Auth: {{backendToken}}

### 2. 快速收藏任务（第一次 - 应该收藏成功）
POST {{baseUrl}}/api/backend/task-folders/quick-favorite/{{taskId}}
Content-Type: application/json
Liangma-Auth: {{backendToken}}

### 3. 查询任务收藏状态（应该显示已收藏）
GET {{baseUrl}}/api/backend/task-folders/task-favorite-status/{{taskId}}
Liangma-Auth: {{backendToken}}

### 4. 快速收藏任务（第二次 - 应该取消收藏）
POST {{baseUrl}}/api/backend/task-folders/quick-favorite/{{taskId}}
Content-Type: application/json
Liangma-Auth: {{backendToken}}

### 5. 批量查询收藏状态
POST {{baseUrl}}/api/backend/task-folders/batch-favorite-status
Content-Type: application/json
Liangma-Auth: {{backendToken}}

[1, 2, 3, 4, 5]

### ========================================
### 期望结果
### ========================================

### 步骤1: 应该返回收藏夹列表（可能为空）
# {
#   "code": 200,
#   "data": [...],
#   "success": true
# }

### 步骤2: 第一次收藏应该成功
# {
#   "code": 200,
#   "data": {
#     "favorited": true,
#     "message": "收藏成功",
#     "folderId": 1,
#     "folderName": "我的收藏"
#   },
#   "success": true
# }

### 步骤3: 查询状态应该显示已收藏
# {
#   "code": 200,
#   "data": {
#     "taskId": 1,
#     "favorited": true,
#     "favoriteCount": 1,
#     "folders": [...]
#   },
#   "success": true
# }

### 步骤4: 第二次收藏应该取消收藏
# {
#   "code": 200,
#   "data": {
#     "favorited": false,
#     "message": "取消收藏成功",
#     "folderId": 1,
#     "folderName": "我的收藏"
#   },
#   "success": true
# }

### 步骤5: 批量查询应该显示任务1为false，其他根据实际情况
# {
#   "code": 200,
#   "data": {
#     "1": false,
#     "2": false,
#     "3": false,
#     "4": false,
#     "5": false
#   },
#   "success": true
# }

### ========================================
### 数据库验证
### ========================================

### 执行以下SQL查看数据变化：

### 查看收藏夹表
# SELECT * FROM lalp_task_folder WHERE deleted = 0;

### 查看收藏关联表
# SELECT * FROM lalp_task_favorite WHERE deleted = 0;

### 查看任务表（确认任务存在）
# SELECT id, plan_name, status FROM lalp_delivery_task WHERE id = 1;
