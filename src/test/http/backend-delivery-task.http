### 获取配送任务列表
GET http://localhost:8080/api/backend/delivery-tasks?page=1&size=10&status=0
Content-Type: application/json
Delivery-Auth: {{authToken}}

### 创建配送任务
POST http://localhost:8080/api/backend/delivery-tasks
Content-Type: application/json
Delivery-Auth: {{authToken}}

{
  "orderId": 1,
  "droneId": "DRONE-001",
  "warehouseId": 1,
  "scheduledTime": "2025-06-15T10:00:00"
}

### 更新配送任务状态
PUT http://localhost:8080/api/backend/delivery-tasks/1/status
Content-Type: application/json


{
  "status": "IN_PROGRESS",
  "remark": "无人机已起飞"
}

### 无人机就位
POST http://localhost:8080/api/backend/delivery-tasks/4/drone-ready
Content-Type: application/json
Delivery-Auth: {{authToken}}

### 标记已取货
POST http://localhost:8080/api/backend/delivery-tasks/4/picked-up
Content-Type: application/json
Delivery-Auth: {{authToken}}

### 标记配送中
POST http://localhost:8080/api/backend/delivery-tasks/4/delivering
Content-Type: application/json
Delivery-Auth: {{authToken}}

### 标记已到达目的地
POST http://localhost:8080/api/backend/delivery-tasks/4/arrived
Content-Type: application/json
Delivery-Auth: {{authToken}}

### 标记已投递
POST http://localhost:8080/api/backend/delivery-tasks/4/delivered
Content-Type: application/json
Delivery-Auth: {{authToken}}
